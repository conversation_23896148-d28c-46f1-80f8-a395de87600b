{"version": 3, "file": "CCI.js", "sourceRoot": "", "sources": ["../../src/oscillators/CCI.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,SAAS,EAAE,cAAc,EAAE,MAAM,wBAAwB,CAAC;AACnE,OAAO,EAAE,GAAG,EAAE,MAAY,wBAAwB,CAAC;AACnD,OAAO,UAAU,MAAM,8BAA8B,CAAC;AAEtD,MAAM,eAAgB,SAAQ,cAAc;CAK3C;AAAA,CAAC;AAGF,MAAM,UAAW,SAAQ,SAAS;IAGhC,YAAY,KAAc;QACxB,KAAK,CAAC,KAAK,CAAC,CAAC;QACb,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,CAAC;QACrB,IAAI,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC;QACvB,IAAI,MAAM,GAAG,KAAK,CAAC,KAAK,CAAC;QACzB,IAAI,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;QAC1B,IAAI,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QACzB,IAAI,QAAQ,GAAG,IAAI,CAAC;QACpB,IAAI,YAAY,GAAG,IAAI,UAAU,CAAC,MAAM,CAAC,CAAC;QAAA,CAAC;QAE3C,IAAI,eAAe,GAAI,IAAI,GAAG,CAAC,EAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAC,EAAE,EAAE,MAAM,EAAG,CAAC,CAAC,EAAE,EAAE,GAAE,MAAM,CAAC,CAAC,CAAA,CAAA,CAAC,EAAC,CAAC,CAAC;QAExF,EAAE,CAAA,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,KAAK,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK,MAAM,CAAC,MAAM,CAAC,CAAE,CAAC,CAAA,CAAC;YACzE,MAAM,CAAC,2CAA2C,CAAC,CAAC;QACtD,CAAC;QAED,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;QAEjB,IAAI,CAAC,SAAS,GAAG,CAAC,QAAQ,CAAC;YACzB,IAAI,IAAI,GAAG,KAAK,CAAC;YACjB,OAAO,IAAI,EAAE,CAAC;gBACZ,IAAI,EAAE,GAAG,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,GAAC,CAAC,CAAA;gBAC9C,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;gBACtB,IAAI,KAAK,GAAG,eAAe,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;gBAC1C,IAAI,aAAa,GAAU,IAAI,CAAC;gBAChC,IAAI,GAAU,CAAC;gBACf,IAAI,GAAG,GAAG,CAAC,CAAC;gBACZ,EAAE,CAAA,CAAC,KAAK,IAAI,SAAS,CAAC,CAAC,CAAC;oBACtB,2GAA2G;oBAC3G,oDAAoD;oBACpD,iCAAiC;oBACjC,GAAG,CAAA,CAAC,IAAI,CAAC,IAAI,YAAY,CAAC,QAAQ,EAAE,CAAC,CAAA,CAAC;wBACpC,GAAG,GAAG,GAAG,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAA;oBACnC,CAAC;oBACD,sDAAsD;oBACtD,aAAa,GAAG,GAAG,GAAG,EAAE,CAAA;oBACxB,GAAG,GAAG,CAAC,EAAE,GAAK,KAAK,CAAC,GAAG,CAAC,QAAQ,GAAG,aAAa,CAAC,CAAA;gBACnD,CAAC;gBACD,IAAI,GAAG,MAAM,GAAG,CAAC;YACnB,CAAC;QACH,CAAC,CAAC,EAAE,CAAC;QAEL,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;QAEtB,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,EAAC,KAAK,EAAE,EAAE;YAC1B,IAAI,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;gBAC/B,IAAI,EAAG,KAAK,CAAC,KAAK,CAAC;gBACnB,GAAG,EAAI,IAAI,CAAC,KAAK,CAAC;gBAClB,KAAK,EAAG,MAAM,CAAC,KAAK,CAAC;aACtB,CAAC,CAAC;YACH,EAAE,CAAA,CAAC,MAAM,CAAC,KAAK,IAAI,SAAS,CAAC,CAAA,CAAC;gBAC5B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YACjC,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAvD8C,CAAC;IAuD/C,CAAC;IAIF,SAAS,CAAC,KAAgB;QACtB,IAAI,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC;QAC9C,EAAE,CAAA,CAAC,MAAM,IAAI,SAAS,CAAC,CAAC,CAAC;YACvB,MAAM,CAAC,MAAM,CAAC;QAChB,CAAC;IACL,CAAC;IAAA,CAAC;;AAPK,aAAS,GAAG,GAAG,CAAC;AAUzB,MAAM,cAAc,KAAc;IAC9B,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC/B,IAAI,MAAM,GAAG,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;IACnC,EAAE,CAAA,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC,CAAC;QACrB,MAAM,CAAC,OAAO,EAAE,CAAC;IACrB,CAAC;IACD,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC/B,MAAM,CAAC,MAAM,CAAC;AAChB,CAAC;AAAA,CAAC"}