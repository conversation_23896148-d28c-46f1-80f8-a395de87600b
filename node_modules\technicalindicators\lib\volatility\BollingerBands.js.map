{"version": 3, "file": "BollingerBands.js", "sourceRoot": "", "sources": ["../../src/volatility/BollingerBands.ts"], "names": [], "mappings": "AAAA,YAAY,CAAA;AACZ,OAAO,EAAE,SAAS,EAAE,cAAc,EAAE,MAAM,wBAAwB,CAAC;AACnE,OAAO,EAAE,GAAG,EAAE,MAAO,wBAAwB,CAAC;AAC9C,OAAO,EAAE,EAAE,EAAE,MAAM,aAAa,CAAC;AACjC,MAAM,0BAA2B,SAAQ,cAAc;CAItD;AAAA,CAAC;AAEF,MAAM,2BAA4B,SAAQ,cAAc;CAKvD;AAAA,CAAC;AAEF,MAAM,qBAAsB,SAAQ,SAAS;IAEzC,YAAY,KAAyB;QACjC,KAAK,CAAC,KAAK,CAAC,CAAC;QACb,IAAI,MAAM,GAAG,KAAK,CAAC,MAAM,CAAA;QACzB,IAAI,UAAU,GAAG,KAAK,CAAC,MAAM,CAAC;QAC9B,IAAI,MAAM,GAAO,KAAK,CAAC,MAAM,CAAC;QAC9B,IAAI,MAAM,GAAO,IAAI,CAAC,MAAM,CAAC;QAE7B,IAAI,GAAG,EAAC,EAAE,CAAC;QAEX,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;QAEjB,GAAG,GAAG,IAAI,GAAG,CAAC,EAAC,MAAM,EAAG,MAAM,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAG,CAAC,CAAC,EAAE,EAAE,GAAE,MAAM,CAAC,CAAC,CAAA,CAAA,CAAC,EAAC,CAAC,CAAC;QACzE,EAAE,GAAI,IAAI,EAAE,CAAC,EAAC,MAAM,EAAG,MAAM,EAAE,MAAM,EAAG,EAAE,EAAE,MAAM,EAAG,CAAC,CAAC,EAAE,EAAE,GAAE,MAAM,CAAC,CAAC,CAAA,CAAA,CAAC,EAAC,CAAC,CAAC;QAEzE,IAAI,CAAC,SAAS,GAAG,CAAC,QAAQ,CAAC;YACvB,IAAI,MAAM,CAAC;YACX,IAAI,IAAI,CAAC;YACT,IAAI,OAAO,CAAC;YACZ,IAAI,MAAM,CAAC;YACX,IAAI,GAAG,KAAK,CAAC;YACb,OAAO,IAAI,EAAE,CAAC;gBACV,OAAO,GAAG,GAAG,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;gBAC9B,MAAM,GAAI,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;gBAC7B,EAAE,CAAA,CAAC,OAAO,CAAC,CAAA,CAAC;oBACR,IAAI,MAAM,GAAG,MAAM,CAAC,OAAO,CAAC,CAAC;oBAC7B,IAAI,KAAK,GAAG,MAAM,CAAC,OAAO,GAAG,CAAC,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC;oBAChD,IAAI,KAAK,GAAG,MAAM,CAAC,OAAO,GAAG,CAAC,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC;oBAChD,IAAI,EAAE,GAAU,MAAM,CAAC,CAAC,IAAI,GAAG,KAAK,CAAC,GAAG,CAAC,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC;oBACzD,MAAM,GAAG;wBACL,MAAM,EAAG,MAAM;wBACf,KAAK,EAAI,KAAK;wBACd,KAAK,EAAI,KAAK;wBACd,EAAE,EAAO,EAAE;qBACd,CAAA;gBACL,CAAC;gBACD,IAAI,GAAG,MAAM,MAAM,CAAC;YACxB,CAAC;QACL,CAAC,CAAC,EAAE,CAAC;QAEL,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;QAEtB,UAAU,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE;YACxB,IAAI,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACvC,EAAE,CAAA,CAAC,MAAM,CAAC,KAAK,IAAI,SAAS,CAAC,CAAA,CAAC;gBAC1B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YACnC,CAAC;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IAID,SAAS,CAAC,KAAY;QAClB,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC;IAC5C,CAAC;IAAA,CAAC;;AAJK,wBAAS,GAAG,cAAc,CAAC;AAOtC,MAAM,yBAAyB,KAAyB;IACjD,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC9B,IAAI,MAAM,GAAG,IAAI,cAAc,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;IAC9C,EAAE,CAAA,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC,CAAC;QACrB,MAAM,CAAC,OAAO,EAAE,CAAC;IACrB,CAAC;IACD,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC/B,MAAM,CAAC,MAAM,CAAC;AAClB,CAAC;AAAA,CAAC"}