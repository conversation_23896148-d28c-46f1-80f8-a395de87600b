{"version": 3, "file": "WilderSmoothing.js", "sourceRoot": "", "sources": ["../../src/moving_averages/WilderSmoothing.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,SAAS,EAAkB,MAAM,wBAAwB,CAAC;AACnE,OAAO,EAAE,UAAU,EAAE,MAAM,qBAAqB,CAAC;AAEjD,2CAA2C;AAC3C,MAAM,sBAAuB,SAAQ,SAAS;IAK1C,YAAY,KAAa;QACrB,KAAK,CAAC,KAAK,CAAC,CAAC;QACb,IAAI,CAAC,MAAM,GAAI,KAAK,CAAC,MAAM,CAAC;QAC5B,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC;QAC1B,IAAI,KAAK,GAAG,CAAC,QAAQ,CAAC,EAAC,MAAa;YAChC,IAAI,IAAI,GAAG,IAAI,UAAU,EAAE,CAAC;YAC5B,IAAI,GAAG,GAAG,CAAC,CAAC;YACZ,IAAI,OAAO,GAAG,CAAC,CAAC;YAChB,IAAI,OAAO,GAAG,KAAK,CAAC;YACpB,IAAI,MAAM,GAAG,CAAC,CAAC;YACf,OAAM,IAAI,EAAC,CAAC;gBACR,EAAE,CAAA,CAAC,OAAO,GAAG,MAAM,CAAC,CAAA,CAAC;oBACjB,OAAO,EAAG,CAAC;oBACX,GAAG,GAAG,GAAG,GAAG,OAAO,CAAC;oBACpB,MAAM,GAAG,SAAS,CAAC;gBACvB,CAAC;gBAAC,IAAI,CAAC,EAAE,CAAA,CAAC,OAAO,IAAI,MAAM,CAAC,CAAA,CAAC;oBACzB,OAAO,EAAG,CAAC;oBACX,GAAG,GAAG,GAAG,GAAG,OAAO,CAAC;oBACpB,MAAM,GAAG,GAAG,CAAC;gBACjB,CAAC;gBACD,IAAI,CAAA,CAAC;oBACD,MAAM,GAAG,MAAM,GAAG,CAAC,MAAM,GAAG,MAAM,CAAC,GAAG,OAAO,CAAC;gBAClD,CAAC;gBACD,OAAO,GAAG,MAAM,MAAM,CAAC;YAC3B,CAAC;QACL,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACpC,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;QACtB,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;QACjB,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE;YACxB,IAAI,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACvC,EAAE,CAAA,CAAC,MAAM,CAAC,KAAK,IAAI,SAAS,CAAC,CAAA,CAAC;gBAC1B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;YAChD,CAAC;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IAID,SAAS,CAAC,KAAY;QAClB,IAAI,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC;QAC9C,EAAE,CAAA,CAAC,MAAM,IAAI,SAAS,CAAC;YACnB,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;IACnC,CAAC;IAAA,CAAC;;AANK,yBAAS,GAAG,eAAe,CAAC;AASvC,MAAM,0BAA0B,KAAa;IACzC,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC/B,IAAI,MAAM,GAAG,IAAI,eAAe,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;IAC/C,EAAE,CAAA,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC,CAAC;QACrB,MAAM,CAAC,OAAO,EAAE,CAAC;IACrB,CAAC;IACD,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC/B,MAAM,CAAC,MAAM,CAAC;AAClB,CAAC;AAAA,CAAC;AAEF,uBAAuB"}