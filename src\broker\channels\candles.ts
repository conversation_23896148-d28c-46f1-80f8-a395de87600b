import { expiryToSeconds, formatChartTimeframe, timeframeToOffset } from '../../utils/formatter'
import type { PocketOption } from '../PocketOption'

export class Candles {
	private static broker: PocketOption

	static init(broker: PocketOption) {
		if (!this.broker) {
			this.broker = broker
		}
	}

	static async call(assetSymbol: string, chartSettings: ChartSettings | null): Promise<void> {
		return new Promise<void>((resolve, reject) => {
			if (!this.broker.getConnectionStatus()) {
				return reject(new Error('Not connected to server'))
			}

			if (!chartSettings) {
				return reject(new Error('Chart settings not found'))
			}

			const timeframe = formatChartTimeframe(chartSettings.chartPeriod)
			const period = expiryToSeconds(timeframe as string)
			const offset = timeframeToOffset(timeframe as string)

			const rand = Math.floor(Math.random() * 90 + 10).toString() // Random number between 10-99 (10-999)
			const cu = Math.floor(Date.now() / 1000) // Current Unix time in seconds
			const t = (cu + 2 * 60 * 60).toString() // Add 2 hours
			const index = parseInt(t + rand, 10) // Concatenate and convert to int

			this.broker.emit('loadHistoryPeriod', {
				asset: assetSymbol,
				index,
				offset,
				period,
				time: Math.floor(Date.now() / 1000) // time size sample:if interval set 1 mean get time 0~1 candle
			})
			resolve()
		})
	}
}
