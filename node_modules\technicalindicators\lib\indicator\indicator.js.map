{"version": 3, "file": "indicator.js", "sourceRoot": "", "sources": ["../../src/indicator/indicator.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,MAAM,IAAI,EAAE,EAAE,MAAM,0BAA0B,CAAC;AAExD,MAAM;CAGL;AAED,MAAM;CAQL;AAED,MAAM;IAGF,YAAY,KAAoB;QAC5B,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,IAAI,EAAE,CAAC;IACrC,CAAC;IACD,MAAM,CAAC,aAAa,CAAC,KAAS;QAC1B,EAAE,CAAA,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC,CAAC;YACrB,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC;YAClD,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC;YAC9C,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC;YAC9C,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC;YAC5C,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC;YAChD,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC;YAClD,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC;QAC5D,CAAC;IACL,CAAC;IAED,SAAS;QACL,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC;IACvB,CAAC;CACJ"}