export function extractAuth(authString: string): { session: string; uid: string } | null {
	try {
		// Remove the "42" prefix and parse the JSON
		const jsonString = authString.substring(2)
		const authArray = JSON.parse(jsonString)

		// Check if the array has the expected structure
		if (Array.isArray(authArray) && authArray.length > 1 && typeof authArray[1] === 'object') {
			const authData = authArray[1]
			const { session, uid } = authData

			// Check if session and uid are present
			if (session && uid) {
				return { session, uid }
			}
		}

		return null
	} catch (error) {
		console.error('Error parsing auth string:', error)
		return null
	}
}
