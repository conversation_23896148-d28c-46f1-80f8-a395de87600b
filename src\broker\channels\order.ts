import type { PocketOption } from '../PocketOption'

export class Order {
	private static broker: PocketOption

	static init(broker: PocketOption) {
		if (!this.broker) {
			this.broker = broker
		}
	}

	static async call(payload: OrderPayload): Promise<void> {
		return new Promise<void>((resolve, reject) => {
			if (!this.broker.getConnectionStatus()) {
				return reject(new Error('Not connected to server'))
			}

			this.broker.emit('openOrder', payload)

			this.broker.once('successopenOrder', (data: unknown[]) => {
				console.log(`[Broker] Order placed: ${JSON.stringify(data)}`)
			})
			resolve()
		})
	}
}
