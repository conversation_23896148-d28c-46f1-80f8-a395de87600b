import express from 'express'
import cors from 'cors'
import helmet from 'helmet'
import morgan from 'morgan'
// import dotenv from 'dotenv'
import apiRoutes from './src/routes'
import { logger } from './src/utils/logger'

// // Load environment variables
// dotenv.config()

const app = express()
const port = process.env.PORT || 3000

// Middleware
app.use(express.json())
app.use(cors())
app.use(helmet())
app.use(morgan('dev'))

// Routes
app.use('/api', apiRoutes)

// Root endpoint
app.get('/', (_req, res) => {
	res.json({
		message: 'PocketOption API Server',
		version: '1.0.0',
		endpoints: {
			balance: '/api/balance',
			status: '/api/status',
			health: '/api/health'
		}
	})
})

// Error handling middleware
app.use((err: any, _req: any, res: any, _next: any) => {
	logger.error('Express', 'Unhandled error', err)
	res.status(500).json({
		error: 'Internal server error',
		message: process.env.NODE_ENV === 'development' ? err.message : 'Something went wrong'
	})
})

// Start server
app.listen(port, () => {
	logger.success('Server', `Started on port ${port}`)
	logger.info('Server', 'Available endpoints:')
	logger.info('Server', '  GET / - API information')
	logger.info('Server', '  GET /api/health - Health check')
	logger.info('Server', '  GET /api/status - Connection status')
	logger.info('Server', '  GET /api/balance - Account balance')
})
