# PocketOption API Documentation

## Overview

The enhanced PocketOption API provides a robust, type-safe wrapper around the PocketOption broker with proper error handling, logging, and connection management.

## Key Improvements

### Fixed Issues
1. **Critical Bug Fix**: Removed undefined `globalValue.balance` reference that caused runtime errors
2. **Instance vs Static Property**: Fixed broker property declaration and usage
3. **Type Safety**: Added proper TypeScript types and error handling
4. **Connection Management**: Added connection state tracking and validation

### Enhanced Features
1. **Comprehensive Error Handling**: All methods now properly catch and handle errors
2. **Logging Integration**: Uses the existing logger utility for consistent logging
3. **Connection State Management**: Tracks connection status and validates before operations
4. **Enhanced Broker Class**: Improved PocketOption broker with timeout handling and balance updates
5. **REST API Endpoints**: Enhanced routes with proper error responses and status codes

## API Classes

### PocketOptionApi

The main API wrapper class that provides a clean interface to the PocketOption broker.

#### Constructor
```typescript
constructor(broker: PocketOption)
```

#### Methods

##### `connect(): Promise<void>`
Establishes connection to the PocketOption broker.
- **Returns**: Promise that resolves when connected
- **Throws**: Error if connection fails

##### `getBalance(): Promise<number>`
Retrieves the current account balance.
- **Returns**: Promise resolving to current balance
- **Throws**: Error if not connected or operation fails

##### `emit(event: string, data: unknown): Promise<void>`
Emits an event to the broker.
- **Parameters**: 
  - `event`: Event name
  - `data`: Data to send
- **Throws**: Error if not connected or operation fails

##### `on(event: string, callback: (data: unknown[]) => void): Promise<void>`
Listens for events from the broker.
- **Parameters**:
  - `event`: Event name to listen for
  - `callback`: Function to execute when event is received
- **Throws**: Error if not connected or operation fails

##### `isApiConnected(): boolean`
Checks if the API is connected to the broker.
- **Returns**: `true` if connected, `false` otherwise

##### `getBroker(): PocketOption`
Returns the underlying broker instance.
- **Returns**: PocketOption broker instance
- **Note**: Use with caution for direct broker access

##### `disconnect(): Promise<void>`
Disconnects from the broker and cleans up resources.

### Enhanced PocketOption Broker

#### New Methods

##### `getConnectionStatus(): boolean`
Returns the current connection status.

##### `disconnect(): void`
Properly disconnects from the server.

##### `updateBalance(newBalance: number): void`
Manually updates the balance (useful for testing or external balance updates).

#### Enhanced Features
- **Connection Timeout**: 10-second timeout for connection attempts
- **Balance Tracking**: Automatic balance updates from server events
- **Better Error Handling**: Comprehensive error catching and logging
- **Connection State Management**: Proper tracking of connection status

## REST API Endpoints

### GET `/api/balance`
Retrieves the current account balance.

**Response (Success - 200)**:
```json
{
  "success": true,
  "balance": 1000.50,
  "timestamp": "2025-07-01T12:00:00.000Z"
}
```

**Response (Service Unavailable - 503)**:
```json
{
  "error": "API not connected to broker",
  "message": "Please try again later"
}
```

**Response (Error - 500)**:
```json
{
  "error": "Failed to get balance",
  "message": "Specific error message"
}
```

### GET `/api/status`
Returns the API connection status.

**Response (200)**:
```json
{
  "connected": true,
  "timestamp": "2025-07-01T12:00:00.000Z"
}
```

### GET `/api/health`
Health check endpoint for monitoring.

**Response (200)**:
```json
{
  "status": "ok",
  "service": "pocket-option-api",
  "timestamp": "2025-07-01T12:00:00.000Z",
  "broker_connected": true
}
```

## Usage Examples

### Basic Usage
```typescript
import { PocketOptionApi } from './src/api'
import { PocketOption } from './src/broker/PocketOption'

// Create broker and API instances
const broker = new PocketOption(process.env.SSID as string, true)
const api = new PocketOptionApi(broker)

// Connect and use
try {
  await api.connect()
  console.log('Connected:', api.isApiConnected())
  
  const balance = await api.getBalance()
  console.log('Balance:', balance)
  
  // Listen for events
  await api.on('balance', (data) => {
    console.log('Balance update:', data)
  })
  
} catch (error) {
  console.error('Error:', error)
}
```

### Error Handling
```typescript
try {
  const balance = await api.getBalance()
  console.log('Balance:', balance)
} catch (error) {
  if (error.message.includes('not connected')) {
    console.log('Need to connect first')
    await api.connect()
  } else {
    console.error('Unexpected error:', error)
  }
}
```

## Testing

Run the test suite:
```bash
bun run src/tests/api.test.ts
```

The test suite includes:
- Basic functionality tests
- Error handling validation
- Connection state management
- Broker enhancement verification

## Environment Variables

- `SSID`: PocketOption session ID for authentication
- `DEMO`: Set to 'false' for live trading, 'true' for demo (default: true)
- `PORT`: Server port (default: 3000)

## Error Handling Strategy

1. **Connection Validation**: All operations check connection status first
2. **Graceful Degradation**: API returns appropriate HTTP status codes
3. **Comprehensive Logging**: All errors are logged with context
4. **Type Safety**: TypeScript ensures compile-time error prevention
5. **Timeout Management**: Connection attempts have reasonable timeouts

## Future Enhancements

1. **Reconnection Logic**: Automatic reconnection on connection loss
2. **Rate Limiting**: Prevent API abuse
3. **WebSocket Events**: Real-time event streaming to clients
4. **Trading Operations**: Add methods for placing trades
5. **Historical Data**: Methods for retrieving market data
