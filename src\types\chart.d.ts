interface ChartInfo {
	chartID: string
	settings: ChartSettings
}

// Raw version of the data
interface ChartsData {
	chart_id: string
	settings: ChartSettings | string
}

// Raw version of the settings
interface ChartSettings {
	chartId: string
	chartType: number
	chartPeriod: number
	candlesTimer: boolean
	symbol: string
	demoDealAmount: number
	liveDealAmount: number
	enabledTradeMonitor: boolean
	enabledRatingWidget: boolean
	isVisible: boolean
	fastTimeframe: number
	enabledAutoscroll: boolean
	enabledGridSnap: boolean
	minimizedTradePanel: boolean
	fastCloseAt: number
	enableQuickAutoOffset: boolean
	quickAutoOffsetValue: number
	showArea: boolean
	percentAmount: number
}
