import { logger } from './logger'

export const formatData = (data: unknown): unknown | null => {
	try {
		if (data instanceof Buffer || data instanceof ArrayBuffer) {
			const json = data.toString('utf-8')

			return JSON.parse(json)
		}

		return data
	} catch (err) {
		logger.error(
			`Formatter`,
			'Failed to format data',
			err instanceof Error ? `${err.message}\r\n${err.stack}` : `Unknown error`
		)
		return null
	}
}

/**
 * Formats the expiry to the corresponding seconds
 * @param expiry - The expiry to format e.g. S30, M1, H1, etc.
 * @returns The corresponding seconds e.g. 30, 60, 3600, etc.
 */
export const expiryToSeconds = (expiry: string): number => {
	const timeValue = parseInt(expiry.slice(1), 10)
	const periodType = expiry.charAt(0)

	switch (periodType) {
		case 'M':
			return timeValue * 60
		case 'S':
			return timeValue
		case 'H':
			return timeValue * 3600
		default:
			console.warn(`Invalid expiry format: ${expiry} - defaulting to 1 minute (M1)`)
			return 60 // Default to 1 minute if the format is invalid
	}
}

/**
 * Formats the chart period to the corresponding timeframe
 * @param chartPeriod - The chart period to format e.g. 0, 1, 2, etc.
 * @returns The corresponding timeframe e.g. S5, M1, H1, etc.
 */
export const formatChartTimeframe = (chartPeriod: number): string | undefined => {
	const chartPeriodMap: Record<number, string> = {
		0: `S5`,
		1: `S10`,
		2: `S15`,
		3: `S30`,
		4: `M1`,
		13: `M2`,
		14: `M3`,
		6: `M5`,
		7: `M10`,
		8: `M15`,
		9: `M30`,
		10: `H1`,
		11: `H4`,
		12: `D1`
	}

	return chartPeriodMap[chartPeriod]
}

/**
 * Formats the timeframe to the corresponding offset
 * @param timeframe - The timeframe to format e.g. S5, M1, H1, etc.
 * @returns The corresponding offset e.g. 1000, 2000, 3000, etc.
 */
export const timeframeToOffset = (timeframe: string | number): string | number | undefined => {
	const timeframeMap: Record<string, number> = {
		S5: 1000,
		S10: 2000,
		S15: 3000,
		S30: 6000,
		M1: 9000,
		M2: 18000,
		M3: 27000,
		M5: 45000,
		M10: 90000,
		M15: 135000,
		M30: 270000,
		H1: 540000,
		H4: 2160000,
		D1: 12960000
	}
	if (typeof timeframe === 'string') return timeframeMap[timeframe]

	for (const [key, value] of Object.entries(timeframeMap)) {
		if (value === timeframe) return key
	}
	return undefined
}
