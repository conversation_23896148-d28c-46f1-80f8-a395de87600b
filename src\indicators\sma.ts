// import { SMA  } from 'technicalindicators'

// export function sma(input: { period: number; values: number[] }): number[] {
// 	const { period, values } = input
// 	const result = []
// 	let sum = 0
// 	let counter = 1

// 	for (let i = 0; i < values.length; i++) {
// 		sum += values[i]
// 		if (counter < period) {
// 			counter++
// 		} else {
// 			result.push(sum / period)
// 			sum -= values[i - period + 1]
// 		}
// 	}

// 	return result
// }
