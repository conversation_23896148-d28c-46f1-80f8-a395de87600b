{"name": "@types/node", "version": "6.14.13", "description": "TypeScript definitions for Node.js", "license": "MIT", "contributors": [{"name": "Microsoft TypeScript", "url": "https://github.com/Microsoft", "githubUsername": "Microsoft"}, {"name": "DefinitelyTyped", "url": "https://github.com/DefinitelyTyped", "githubUsername": "DefinitelyTyped"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/WilcoBakker", "githubUsername": "WilcoBakker"}, {"name": "<PERSON>", "url": "https://github.com/inlined", "githubUsername": "inlined"}, {"name": "<PERSON>", "url": "https://github.com/eps1lon", "githubUsername": "eps1lon"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/Alorel", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/KSXGitHub", "githubUsername": "KSXGitHub"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Archcry", "githubUsername": "Archcry"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/node"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "bc67298ac72ea3924d614c0dffa81f062cb2b94fee147c6d54cd8e62cd4326ca", "typeScriptVersion": "3.2"}