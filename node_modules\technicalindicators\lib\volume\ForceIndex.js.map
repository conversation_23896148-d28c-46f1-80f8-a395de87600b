{"version": 3, "file": "ForceIndex.js", "sourceRoot": "", "sources": ["../../src/volume/ForceIndex.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,GAAG,EAAE,MAAM,wBAAwB,CAAC;AAE7C,OAAO,EAAE,SAAS,EAAE,cAAc,EAAE,MAAM,wBAAwB,CAAC;AAEnE,MAAM,sBAAuB,SAAQ,cAAc;IAAnD;;QAGE,WAAM,GAAY,CAAC,CAAC;IACtB,CAAC;CAAA;AAAA,CAAC;AAGF,MAAM,iBAAkB,SAAQ,SAAS;IAGvC,YAAY,KAAqB;QAC/B,KAAK,CAAC,KAAK,CAAC,CAAC;QACb,IAAI,MAAM,GAAG,KAAK,CAAC,KAAK,CAAC;QACzB,IAAI,OAAO,GAAG,KAAK,CAAC,MAAM,CAAC;QAC3B,IAAI,MAAM,GAAG,KAAK,CAAC,MAAM,IAAI,CAAC,CAAA;QAE9B,EAAE,CAAA,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,KAAK,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAA,CAAC;YACxC,MAAM,CAAC,yCAAyC,CAAC,CAAC;QACpD,CAAC;QACD,IAAI,aAAa,GAAG,IAAI,GAAG,CAAC,EAAE,MAAM,EAAG,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC,CAAA;QAC5D,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;QAEjB,IAAI,CAAC,SAAS,GAAG,CAAC,QAAQ,CAAC;YACzB,IAAI,YAAY,GAAG,KAAK,CAAC;YACzB,IAAI,IAAI,GAAG,KAAK,CAAC;YACjB,IAAI,UAAU,CAAC;YACf,OAAO,IAAI,EAAE,CAAC;gBACZ,UAAU,GAAG,CAAC,IAAI,CAAC,KAAK,GAAG,YAAY,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC;gBAC7D,YAAY,GAAG,IAAI,CAAC;gBACpB,IAAI,GAAG,MAAM,aAAa,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;YACnD,CAAC;QACH,CAAC,CAAC,EAAE,CAAC;QAEL,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;QAEtB,OAAO,CAAC,OAAO,CAAC,CAAC,IAAI,EAAC,KAAK,EAAE,EAAE;YAC7B,IAAI,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;gBAC/B,KAAK,EAAG,MAAM,CAAC,KAAK,CAAC;gBACrB,MAAM,EAAG,OAAO,CAAC,KAAK,CAAC;aACxB,CAAC,CAAC;YACH,EAAE,CAAA,CAAC,MAAM,CAAC,KAAK,IAAI,SAAS,CAAC,CAAA,CAAC;gBAC5B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YACjC,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAnC8C,CAAC;IAmC/C,CAAC;IAIF,SAAS,CAAC,KAAiB;QACvB,IAAI,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC;QAC9C,EAAE,CAAA,CAAC,MAAM,IAAI,SAAS,CAAC,CAAC,CAAC;YACvB,MAAM,CAAC,MAAM,CAAC;QAChB,CAAC;IACL,CAAC;IAAA,CAAC;;AAPK,oBAAS,GAAG,UAAU,CAAC;AAUhC,MAAM,qBAAqB,KAAqB;IAC5C,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC/B,IAAI,MAAM,GAAG,IAAI,UAAU,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;IAC1C,EAAE,CAAA,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC,CAAC;QACrB,MAAM,CAAC,OAAO,EAAE,CAAC;IACrB,CAAC;IACD,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC/B,MAAM,CAAC,MAAM,CAAC;AAChB,CAAC;AAAA,CAAC"}