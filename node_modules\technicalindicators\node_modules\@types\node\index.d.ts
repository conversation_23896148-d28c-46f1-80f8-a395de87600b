// Type definitions for Node.js 6.14
// Project: http://nodejs.org/
// Definitions by: Microsoft TypeScript <https://github.com/Microsoft>
//                 DefinitelyTyped <https://github.com/DefinitelyTyped>
//                 <PERSON><PERSON><PERSON> <https://github.com/Wilco<PERSON><PERSON>ker>
//                 <PERSON> <https://github.com/inlined>
//                 <PERSON> <https://github.com/eps1lon>
//                 Alorel <https://github.com/Alorel>
//                 <PERSON>àng <PERSON>ă<PERSON> <https://github.com/KSXGitHub>
//                 <PERSON><PERSON> <https://github.com/Archcry>
// Definitions: https://github.com/DefinitelyTyped/DefinitelyTyped
// NOTE: These definitions support NodeJS and TypeScript 3.2 and above.

// NOTE: TypeScript version-specific augmentations can be found in the following paths:
//          - ~/base.d.ts         - Shared definitions common to all TypeScript versions
//          - ~/index.d.ts        - Definitions specific to TypeScript 2.1
//          - ~/ts3.2/index.d.ts  - Definitions specific to TypeScript 3.2

// Reference required types from the default lib:
/// <reference lib="es2015" />

// Base definitions for all NodeJS modules that are not specific to any version of TypeScript:
/// <reference path="base.d.ts" />

// TypeScript 3.2-specific augmentations:
