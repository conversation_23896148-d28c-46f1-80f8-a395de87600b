{"version": 3, "file": "EMA.js", "sourceRoot": "", "sources": ["../../src/moving_averages/EMA.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAkB,MAAM,wBAAwB,CAAC;AACnE,OAAO,EAAW,GAAG,EAAE,MAAM,OAAO,CAAC;AAGrC,MAAM,UAAW,SAAQ,SAAS;IAK9B,YAAY,KAAa;QACrB,KAAK,CAAC,KAAK,CAAC,CAAC;QACb,IAAI,MAAM,GAAG,KAAK,CAAC,MAAM,CAAA;QACzB,IAAI,UAAU,GAAG,KAAK,CAAC,MAAM,CAAC;QAC9B,IAAI,QAAQ,GAAG,CAAC,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;QAClC,IAAI,GAAO,CAAC;QAEZ,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;QAEjB,GAAG,GAAG,IAAI,GAAG,CAAC,EAAC,MAAM,EAAG,MAAM,EAAE,MAAM,EAAE,EAAE,EAAC,CAAC,CAAC;QAE7C,IAAI,KAAK,GAAG,CAAC,QAAQ,CAAC;YAClB,IAAI,IAAI,GAAI,KAAK,CAAC;YAClB,IAAI,OAAO,CAAC;YACZ,OAAO,IAAI,EAAE,CAAC;gBACV,EAAE,CAAA,CAAC,OAAO,KAAK,SAAS,IAAI,IAAI,KAAK,SAAS,CAAC,CAAA,CAAC;oBAC5C,OAAO,GAAG,CAAC,CAAC,IAAI,GAAG,OAAO,CAAC,GAAG,QAAQ,CAAC,GAAG,OAAO,CAAC;oBAClD,IAAI,GAAG,MAAM,OAAO,CAAC;gBACzB,CAAC;gBAAA,IAAI,CAAC,CAAC;oBACH,IAAI,GAAG,KAAK,CAAC;oBACb,OAAO,GAAG,GAAG,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA;oBAC7B,EAAE,CAAA,CAAC,OAAO,CAAC;wBACP,IAAI,GAAG,MAAM,OAAO,CAAC;gBAC7B,CAAC;YACL,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,SAAS,GAAG,KAAK,EAAE,CAAC;QAEzB,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;QACtB,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;QAEtB,UAAU,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE;YACxB,IAAI,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACvC,EAAE,CAAA,CAAC,MAAM,CAAC,KAAK,IAAI,SAAS,CAAC,CAAA,CAAC;gBAC1B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;YAChD,CAAC;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IAID,SAAS,CAAC,KAAY;QAClB,IAAI,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC;QAC9C,EAAE,CAAA,CAAC,MAAM,IAAI,SAAS,CAAC;YACnB,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;IACnC,CAAC;IAAA,CAAC;;AANK,aAAS,GAAG,GAAG,CAAC;AAS3B,MAAM,cAAc,KAAa;IACzB,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC/B,IAAI,MAAM,GAAG,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;IACnC,EAAE,CAAA,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC,CAAC;QACrB,MAAM,CAAC,OAAO,EAAE,CAAC;IACrB,CAAC;IACD,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC/B,MAAM,CAAC,MAAM,CAAC;AAClB,CAAC"}