export class Region {
	private static readonly REGIONS: Record<string, string> = {
		DEMO: 'wss://demo-api-eu.po.market',
		EUROPA: 'wss://api-eu.po.market',
		SEYCHELLES: 'wss://api-sc.po.market',
		HONGKONG: 'wss://api-hk.po.market',
		SERVER1: 'wss://api-spb.po.market',
		FRANCE2: 'wss://api-fr2.po.market',
		UNITED_STATES4: 'wss://api-us4.po.market',
		UNITED_STATES3: 'wss://api-us3.po.market',
		UNITED_STATES2: 'wss://api-us2.po.market',
		UNITED_STATES: 'wss://api-us-north.po.market',
		RUSSIA: 'wss://api-msk.po.market',
		SERVER2: 'wss://api-l.po.market',
		INDIA: 'wss://api-in.po.market',
		FRANCE: 'wss://api-fr.po.market',
		FINLAND: 'wss://api-fin.po.market',
		SERVER3: 'wss://api-c.po.market',
		ASIA: 'wss://api-asia.po.market',
		SERVER4: 'wss://api-us-south.po.market'
	}
	private static readonly ORIGINS: Record<string, string> = {
		DEMO: 'https://pocketoption.com'
	}

	public static readonly DEMO_REGION: string = 'wss://demo-api-eu.po.market'
	public static readonly SOCKET_OPTIONS: any = {
		transports: ['websocket'],
		query: {
			EIO: '4',
			transport: ['websocket']
		},
		extraHeaders: {
			Origin: Region.ORIGINS.DEMO
		},
		path: '/socket.io/'
	}

	static get(key: keyof typeof Region.REGIONS): string {
		const endpoint = Region.REGIONS[key as string]
		if (!endpoint) {
			throw new Error(`Region ${key} not found`)
		}

		return endpoint
	}

	static getRegion(randomize: boolean = true): string[] {
		const endpoint = Object.values(Region.REGIONS)
		if (randomize) {
			return endpoint.sort(() => Math.random() - 0.5)
		}

		return endpoint
	}
}
