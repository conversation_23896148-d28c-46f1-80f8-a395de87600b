{"version": 3, "file": "PSAR.js", "sourceRoot": "", "sources": ["../../src/momentum/PSAR.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,cAAc,EAAE,SAAS,EAAE,MAAM,wBAAwB,CAAC;AAEnE,YAAY,CAAA;AAEZ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA8BE;AACF,MAAM,gBAAiB,SAAQ,cAAc;CAK5C;AAAA,CAAC;AAEF,MAAM,WAAY,SAAQ,SAAS;IAGjC,YAAa,KAAe;QAC1B,KAAK,CAAC,KAAK,CAAC,CAAC;QAEb,IAAI,KAAK,GAAG,KAAK,CAAC,IAAI,IAAI,EAAE,CAAC;QAC7B,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,IAAI,EAAE,CAAC;QAE3B,IAAI,KAAK,GAAG,QAAQ,CAAC,EAAE,IAAW,EAAE,GAAU;YAC5C,IAAI,IAAI,EAAE,OAAO,EAAE,GAAG,EAAE,QAAQ,CAAC;YAEjC,IAAI,EAAE,GAAG,IAAI,CAAC;YACd,IAAI,KAAK,GAAG,IAAI,CAAC;YACjB,IAAI,IAAI,GAAG,KAAK,CAAC;YACjB,OAAM,IAAI,EAAE,CAAC;gBACX,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;oBACT,GAAG,GAAG,GAAG,GAAG,KAAK,GAAG,CAAC,OAAO,GAAG,GAAG,CAAC,CAAC;oBAEpC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;wBACP,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,QAAQ,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;wBAE5C,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC,CAAC,CAAC;4BACxB,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC;4BACpB,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,GAAG,IAAI,EAAE,GAAG,CAAC,CAAC;wBACtC,CAAC;wBAAA,CAAC;oBACJ,CAAC;oBAAC,IAAI,CAAC,CAAC;wBACN,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;wBAE9C,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,GAAG,OAAO,CAAC,CAAC,CAAC;4BACvB,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC;4BACnB,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,GAAG,IAAI,EAAE,GAAG,CAAC,CAAC;wBACtC,CAAC;oBACH,CAAC;oBAED,EAAE,CAAC,CAAC,CAAC,EAAE,IAAI,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;wBACvD,KAAK,GAAG,IAAI,CAAC;wBACb,GAAG,GAAG,OAAO,CAAC;wBACd,EAAE,GAAG,CAAC,EAAE,CAAC;wBAET,OAAO,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;oBACvC,CAAC;gBACH,CAAC;gBAAC,IAAI,CAAC,CAAC;oBACN,iEAAiE;oBACjE,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC;oBAAC,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC;gBACtC,CAAC;gBAED,QAAQ,GAAG,IAAI,CAAC;gBAChB,EAAE,CAAC,CAAC,IAAI,CAAC;oBAAC,IAAI,GAAG,IAAI,CAAC;gBACtB,IAAI,GAAG,MAAM,GAAG,CAAC;YACnB,CAAC;QACH,CAAC,CAAC;QAEF,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;QACjB,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC;QAC9C,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;QAEtB,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE;YAC3B,IAAI,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;gBAC/B,IAAI,EAAE,KAAK,CAAC,KAAK,CAAC;gBAClB,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC;aACjB,CAAC,CAAC;YACH,EAAE,CAAA,CAAC,MAAM,CAAC,KAAK,KAAK,SAAS,CAAC,CAAA,CAAC;gBAC7B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YACjC,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAAA,CAAC;IAIF,SAAS,CAAE,KAAe;QACxB,IAAI,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC5C,EAAE,CAAA,CAAC,UAAU,CAAC,KAAK,KAAK,SAAS,CAAC;YAChC,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC;IAC5B,CAAC;IAAA,CAAC;;AANK,cAAS,GAAG,IAAI,CAAC;AAS1B,MAAM,eAAe,KAAe;IAC5B,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC/B,IAAI,MAAM,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;IACpC,EAAE,CAAA,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC,CAAC;QACrB,MAAM,CAAC,OAAO,EAAE,CAAC;IACrB,CAAC;IACD,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC/B,MAAM,CAAC,MAAM,CAAC;AAClB,CAAC;AAAA,CAAC"}