{"modelTopology": {"training_config": {"metrics": [], "loss": "categorical_crossentropy", "optimizer_config": {"class_name": "SGD", "config": {"nesterov": true, "lr": 0.009999999776482582, "momentum": 0.8999999761581421, "decay": 9.999999974752427e-07}}, "sample_weight_mode": null, "loss_weights": null}, "keras_version": "2.2.2", "model_config": {"class_name": "Sequential", "config": [{"class_name": "<PERSON><PERSON>", "config": {"kernel_initializer": {"class_name": "VarianceScaling", "config": {"distribution": "uniform", "scale": 1.0, "seed": null, "mode": "fan_avg"}}, "name": "dense_1", "kernel_constraint": null, "bias_regularizer": null, "bias_constraint": null, "dtype": "float32", "activation": "relu", "trainable": true, "kernel_regularizer": null, "bias_initializer": {"class_name": "Zeros", "config": {}}, "units": 256, "batch_input_shape": [null, 400], "use_bias": true, "activity_regularizer": null}}, {"class_name": "Dropout", "config": {"rate": 0.25, "noise_shape": null, "trainable": true, "seed": null, "name": "dropout_1"}}, {"class_name": "<PERSON><PERSON>", "config": {"kernel_initializer": {"class_name": "VarianceScaling", "config": {"distribution": "uniform", "scale": 1.0, "seed": null, "mode": "fan_avg"}}, "name": "dense_2", "kernel_constraint": null, "bias_regularizer": null, "bias_constraint": null, "activation": "relu", "trainable": true, "kernel_regularizer": null, "bias_initializer": {"class_name": "Zeros", "config": {}}, "units": 256, "use_bias": true, "activity_regularizer": null}}, {"class_name": "Dropout", "config": {"rate": 0.25, "noise_shape": null, "trainable": true, "seed": null, "name": "dropout_2"}}, {"class_name": "<PERSON><PERSON>", "config": {"kernel_initializer": {"class_name": "VarianceScaling", "config": {"distribution": "uniform", "scale": 1.0, "seed": null, "mode": "fan_avg"}}, "name": "dense_3", "kernel_constraint": null, "bias_regularizer": null, "bias_constraint": null, "activation": "relu", "trainable": true, "kernel_regularizer": null, "bias_initializer": {"class_name": "Zeros", "config": {}}, "units": 256, "use_bias": true, "activity_regularizer": null}}, {"class_name": "Dropout", "config": {"rate": 0.25, "noise_shape": null, "trainable": true, "seed": null, "name": "dropout_3"}}, {"class_name": "<PERSON><PERSON>", "config": {"kernel_initializer": {"class_name": "VarianceScaling", "config": {"distribution": "uniform", "scale": 1.0, "seed": null, "mode": "fan_avg"}}, "name": "dense_4", "kernel_constraint": null, "bias_regularizer": null, "bias_constraint": null, "activation": "relu", "trainable": true, "kernel_regularizer": null, "bias_initializer": {"class_name": "Zeros", "config": {}}, "units": 256, "use_bias": true, "activity_regularizer": null}}, {"class_name": "Dropout", "config": {"rate": 0.25, "noise_shape": null, "trainable": true, "seed": null, "name": "dropout_4"}}, {"class_name": "<PERSON><PERSON>", "config": {"kernel_initializer": {"class_name": "VarianceScaling", "config": {"distribution": "uniform", "scale": 1.0, "seed": null, "mode": "fan_avg"}}, "name": "dense_5", "kernel_constraint": null, "bias_regularizer": null, "bias_constraint": null, "activation": "relu", "trainable": true, "kernel_regularizer": null, "bias_initializer": {"class_name": "Zeros", "config": {}}, "units": 256, "use_bias": true, "activity_regularizer": null}}, {"class_name": "Dropout", "config": {"rate": 0.5, "noise_shape": null, "trainable": true, "seed": null, "name": "dropout_5"}}, {"class_name": "<PERSON><PERSON>", "config": {"kernel_initializer": {"class_name": "VarianceScaling", "config": {"distribution": "uniform", "scale": 1.0, "seed": null, "mode": "fan_avg"}}, "name": "dense_6", "kernel_constraint": null, "bias_regularizer": null, "bias_constraint": null, "activation": "softmax", "trainable": true, "kernel_regularizer": null, "bias_initializer": {"class_name": "Zeros", "config": {}}, "units": 6, "use_bias": true, "activity_regularizer": null}}]}, "backend": "tensorflow"}, "weightsManifest": [{"paths": ["group1-shard1of1"], "weights": [{"dtype": "float32", "shape": [400, 256], "name": "dense_1/kernel"}, {"dtype": "float32", "shape": [256], "name": "dense_1/bias"}, {"dtype": "float32", "shape": [256, 256], "name": "dense_2/kernel"}, {"dtype": "float32", "shape": [256], "name": "dense_2/bias"}, {"dtype": "float32", "shape": [256, 256], "name": "dense_3/kernel"}, {"dtype": "float32", "shape": [256], "name": "dense_3/bias"}, {"dtype": "float32", "shape": [256, 256], "name": "dense_4/kernel"}, {"dtype": "float32", "shape": [256], "name": "dense_4/bias"}, {"dtype": "float32", "shape": [256, 256], "name": "dense_5/kernel"}, {"dtype": "float32", "shape": [256], "name": "dense_5/bias"}, {"dtype": "float32", "shape": [256, 6], "name": "dense_6/kernel"}, {"dtype": "float32", "shape": [6], "name": "dense_6/bias"}]}]}